/* .caption{
    bottom: 25px;
    left: 25px;
    right: 25px;
} */



.prymry_form{
    margin-top: 80px !important;
}

.title{
    color: #17A2B8;
    margin: 25px;
}

.btn{
    background-color: #24353F !important;
    border: 1px solid #17A2B8 !important;
    color:#17A2B8 ;
}

.btn:hover{
    background-color: #17A2B8 !important;
    border: 1px solid #24353F !important;
    color: #24353F;
}

.prymry_form p{
    color: #17A2B8;
}

.prymry_form p a{
    color: white;
}

.custom-input {
    background-color:#24353F !important ; 
    color: white !important;
}


.custom-input::placeholder {
    color: white !important;
}

/* Welcome Page Styles */
.logout-btn {
    background-color: transparent !important;
    border: 2px solid #17A2B8 !important;
    color: #17A2B8 !important;
    padding: 8px 20px;
    border-radius: 5px;
}

.logout-btn:hover {
    background-color: #17A2B8 !important;
    color: white !important;
}

.min-vh-75 {
    min-height: 75vh;
}

.welcome-title {
    color: #17A2B8;
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.welcome-subtitle {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.account-info-box {
    background-color: rgba(23, 162, 184, 0.1);
    border: 1px solid #17A2B8;
    border-radius: 10px;
    max-width: 500px;
    margin: 0 auto;
}

.info-title {
    color: #17A2B8;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.info-item {
    color: white;
    font-size: 1.1rem;
    text-align: left;
}

.info-item strong {
    color: white;
}
.tiltleWelcome{
    margin-top: 20px;
    margin-left: 20px;
}