// // var elm=document.getElementById("demo");
// var elm=document.getElementsByClassName("col-md-4");
// console.log(elm)



// var xhr=new XMLHttpRequest();
// xhr.open('get',"https://jsonplaceholder.typicode.com/posts");
// xhr.send();
// xhr.responseType="json";
// xhr.addEventListener("load",function(){
//     console.log(xhr.response)
// })








var getName=document.getElementById("Name");
var getEmail=document.getElementById("Email1");
var getPassword=document.getElementById("Password");
var usersList=[];

function createUser(){
    var name=getName.value;
    var email=getEmail.value;
    var password=getPassword.value;

    var user={
        name:name,
        email:email,
        password:password
    }
    var storedUsers=JSON.parse(localStorage.getItem("usersList"))||[];


    usersList.push(user);

    localStorage.setItem("usersList",JSON.stringify(usersList));
    
    alert("Account created successfully!");

    getName.value = "";
    getEmail.value = "";
    getPassword.value = "";
}




var loginEmail=document.getElementById("InputEmail1");
var loginPassword=document.getElementById("InputPassword");

function loginUser(){
    var email=loginEmail.value;
    var password=loginPassword.value;
    var storedUsers=JSON.parse(localStorage.getItem("usersList"))||[];
    var matchedUser =storedUsers.find(function(user){
        return user.email === email && user.password === password;
    })

    if(matchedUser){
        localStorage.setItem("currentUser", JSON.stringify(matchedUser));
        window.location.href = "welcome.html";

    }else {
        alert("Invalid email or password!"); 
    }
}














